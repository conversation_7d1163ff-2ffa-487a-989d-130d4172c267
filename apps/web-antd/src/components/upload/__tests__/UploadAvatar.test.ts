import { mount } from '@vue/test-utils';
import { describe, expect, it, vi } from 'vitest';
import { nextTick } from 'vue';

import UploadAvatar from '../UploadAvatar.vue';

// Mock vue-cropper
vi.mock('vue-cropper/next', () => ({
  VueCropper: {
    name: 'VueCropper',
    template: '<div class="mock-vue-cropper"></div>',
    props: [
      'img',
      'outputSize',
      'outputType',
      'info',
      'full',
      'canMove',
      'canMoveBox',
      'original',
      'autoCrop',
      'autoCropWidth',
      'autoCropHeight',
      'centerBox',
      'high',
      'fixed',
      'fixedNumber',
      'maxImgSize',
      'enlarge',
      'mode',
    ],
    methods: {
      changeScale: vi.fn(),
      rotateLeft: vi.fn(),
      rotateRight: vi.fn(),
      refresh: vi.fn(),
      getCropBlob: vi.fn((callback) => {
        // Mock blob data
        const mockBlob = new Blob(['mock image data'], { type: 'image/png' });
        callback(mockBlob);
      }),
    },
  },
}));

// Mock FileApi
vi.mock('#/api/common/file', () => ({
  FileApi: {
    uploadImage: vi.fn().mockResolvedValue({
      fileName: 'test.png',
      fileSize: 1024,
      fileUrl: 'http://example.com/test.png',
      fileType: 'image/png',
    }),
  },
}));

// Mock ant-design-vue components
vi.mock('ant-design-vue', () => ({
  Button: { name: 'Button', template: '<button><slot /></button>' },
  message: {
    success: vi.fn(),
    error: vi.fn(),
  },
  Modal: {
    name: 'Modal',
    template: '<div v-if="open"><slot /></div>',
    props: ['open', 'title', 'width', 'footer'],
    emits: ['cancel', 'update:open'],
  },
  Space: { name: 'Space', template: '<div><slot /></div>' },
  Upload: {
    name: 'Upload',
    template: '<div><slot /></div>',
    props: ['customRequest', 'showUploadList', 'accept'],
  },
}));

// Mock @vben/icons
vi.mock('@vben/icons', () => ({
  IconifyIcon: {
    name: 'IconifyIcon',
    template: '<i class="mock-icon"></i>',
    props: ['icon'],
  },
}));

describe('UploadAvatar', () => {
  it('should render without errors', () => {
    const wrapper = mount(UploadAvatar, {
      props: {
        size: 100,
      },
    });

    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.upload-avatar').exists()).toBe(true);
  });

  it('should display upload placeholder when no image is provided', () => {
    const wrapper = mount(UploadAvatar, {
      props: {
        size: 100,
      },
    });

    expect(wrapper.find('.upload-placeholder').exists()).toBe(true);
    expect(wrapper.find('.avatar-preview').exists()).toBe(false);
  });

  it('should display image when value is provided', () => {
    const wrapper = mount(UploadAvatar, {
      props: {
        size: 100,
        value: 'http://example.com/avatar.jpg',
      },
    });

    expect(wrapper.find('.avatar-preview').exists()).toBe(true);
    expect(wrapper.find('.upload-placeholder').exists()).toBe(false);
  });

  it('should set correct avatar size', () => {
    const wrapper = mount(UploadAvatar, {
      props: {
        size: 150,
      },
    });

    const uploadArea = wrapper.find('.upload-area');
    expect(uploadArea.attributes('style')).toContain('width: 150px');
    expect(uploadArea.attributes('style')).toContain('height: 150px');
  });

  it('should handle file selection and show crop modal', async () => {
    const wrapper = mount(UploadAvatar, {
      props: {
        size: 100,
      },
    });

    // Mock FileReader
    const mockFileReader = {
      addEventListener: vi.fn(),
      readAsDataURL: vi.fn(),
      result: 'data:image/jpeg;base64,mock-data',
    };

    global.FileReader = vi.fn(() => mockFileReader) as any;

    // Simulate file selection
    const mockFile = new File(['mock'], 'test.jpg', { type: 'image/jpeg' });
    const customRequest = wrapper.vm.customRequest;

    customRequest({ file: mockFile });

    // Simulate FileReader load event
    const loadHandler = mockFileReader.addEventListener.mock.calls[0][1];
    loadHandler({ target: { result: 'data:image/jpeg;base64,mock-data' } });

    await nextTick();

    expect(wrapper.vm.showCropModal).toBe(true);
    expect(wrapper.vm.originalImageSrc).toBe('data:image/jpeg;base64,mock-data');
  });

  it('should emit success event after successful crop and upload', async () => {
    const wrapper = mount(UploadAvatar, {
      props: {
        size: 100,
      },
    });

    // Set up the component state as if a file was selected
    wrapper.vm.originalFile = new File(['mock'], 'test.jpg', { type: 'image/jpeg' });
    wrapper.vm.cropperRef = {
      getCropBlob: vi.fn((callback) => {
        const mockBlob = new Blob(['mock image data'], { type: 'image/png' });
        callback(mockBlob);
      }),
    };

    await wrapper.vm.confirmCrop();

    expect(wrapper.emitted('success')).toBeTruthy();
    expect(wrapper.emitted('update:value')).toBeTruthy();
  });
});
