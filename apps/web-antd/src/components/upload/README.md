# UploadAvatar 头像上传组件

## 功能特性

- ✅ 图片选择和预览
- ✅ 图片裁剪功能（基于 vue-cropper）
- ✅ 支持缩放、旋转、重置等操作
- ✅ 自动上传裁剪后的图片
- ✅ 支持自定义头像尺寸
- ✅ 支持自定义裁剪框宽高比
- ✅ 响应式设计，支持 Tailwind CSS
- ✅ 完整的错误处理和用户反馈
- ✅ TypeScript 支持

## 使用方法

### 基础用法

```vue
<template>
  <UploadAvatar
    v-model:value="avatarUrl"
    :size="120"
    @success="handleSuccess"
    @error="handleError"
  />
</template>

<script setup>
import { ref } from 'vue';
import { UploadAvatar } from '#/components/upload';

const avatarUrl = ref('');

function handleSuccess(response) {
  console.log('上传成功:', response);
}

function handleError(error) {
  console.error('上传失败:', error);
}
</script>
```

### 带按钮的头像上传

```vue
<template>
  <UploadAvatar
    :size="100"
    :show-button="true"
    @success="handleSuccess"
    @error="handleError"
  />
</template>
```

### 自定义裁剪比例

```vue
<template>
  <!-- 正方形裁剪 -->
  <UploadAvatar
    :size="100"
    :aspect-ratio="1"
    @success="handleSuccess"
  />
  
  <!-- 3:2 比例裁剪 -->
  <UploadAvatar
    :size="100"
    :aspect-ratio="1.5"
    @success="handleSuccess"
  />
</template>
```

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `value` | `string` | `''` | 头像 URL，支持 v-model |
| `size` | `number` | `100` | 头像显示尺寸（像素） |
| `showButton` | `boolean` | `false` | 是否显示上传按钮 |
| `aspectRatio` | `number` | `1` | 裁剪框宽高比 |
| `fixedBox` | `boolean` | `true` | 是否固定裁剪框大小 |
| `enableCompression` | `boolean` | `true` | 是否启用图片压缩 |
| `maxSize` | `number` | `5` | 最大文件大小（MB） |
| `accept` | `string[]` | `['image/jpeg', 'image/png', 'image/gif', 'image/webp']` | 允许的文件类型 |
| `disabled` | `boolean` | `false` | 是否禁用 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `success` | `(response, file)` | 上传成功时触发 |
| `error` | `(error, file)` | 上传失败时触发 |
| `update:value` | `(url)` | 头像 URL 更新时触发 |

## 裁剪功能

组件集成了 vue-cropper 库，提供以下裁剪功能：

- **缩放**：支持鼠标滚轮或按钮缩放
- **旋转**：支持左右旋转 90 度
- **移动**：支持拖拽移动图片和裁剪框
- **重置**：一键重置到初始状态

### 裁剪操作按钮

- 🔍 放大
- 🔍 缩小  
- ↺ 左旋转
- ↻ 右旋转
- 🔄 重置

## 技术实现

- **UI 框架**：Ant Design Vue
- **裁剪库**：vue-cropper 1.1.4
- **样式**：Tailwind CSS
- **图标**：@vben/icons (Carbon 图标集)
- **上传 API**：使用项目的 FileApi.uploadImage

## 文件结构

```
src/components/upload/
├── UploadAvatar.vue          # 头像上传组件
├── types.ts                  # 类型定义
├── index.ts                  # 导出文件
└── __tests__/
    └── UploadAvatar.test.ts  # 测试文件
```

## 样式特性

- 圆形头像显示
- 悬停效果和遮罩层
- 响应式设计
- 支持不同尺寸
- 优雅的加载和错误状态

## 注意事项

1. 确保项目已安装 `vue-cropper` 依赖
2. 需要配置正确的图片上传 API 端点
3. 建议设置合适的文件大小限制
4. 支持的图片格式：JPEG、PNG、GIF、WebP
